# H3六边形网格聚类算法配置文件
# 使用方式：启动时添加 --spring.profiles.active=h3
# 或者在application.yml中设置 spring.profiles.active: h3

# ===================== H3聚类算法配置 =====================

clustering:
  algorithm:
    # 算法类型选择
    # H3: 使用H3六边形网格聚类（推荐，性能和质量更优）
    # KMEANS: 使用传统K-means聚类（兼容模式）
    # AUTO: 自动选择算法（H3优先，失败时降级到K-means）
    type: H3
    
    # 自动降级设置
    # 当H3算法失败或质量不达标时，是否自动降级到K-means
    auto-fallback: true
    
    # 性能对比模式
    # 启用时会同时运行两种算法进行性能对比（仅用于测试和调优）
    # 生产环境建议设置为false
    performance-comparison: false

# ===================== H3算法专用参数 =====================

h3:
  # H3分辨率配置
  resolution:
    # 标准分辨率（六边形边长约0.46公里）
    standard: 8
    # 高密度分辨率（六边形边长约0.17公里）
    high-density: 9
    # 高密度阈值（点/km²），超过此密度使用高密度分辨率
    density-threshold: 50.0
  
  # 路线点数控制
  points-per-route:
    # 目标点数范围：12-15个点/路线
    min: 12
    max: 15
    optimal: 13
    # 扩展容忍度：最大扩展点数
    expansion-tolerance: 3
  
  # 边缘度计算权重
  edge-scoring:
    # 地理边缘性权重（距离中转站远近）
    geographic-weight: 0.6
    # 拓扑边缘性权重（邻居网格数量）
    topological-weight: 0.4
  
  # 质量控制
  quality:
    # 最低质量阈值：目标范围内路线比例
    min-quality-ratio: 0.8
    # 最大路线点数上限（超过此值将被拆分）
    max-route-size: 18
    # 最小路线点数下限（低于此值将被合并）
    min-route-size: 8

# ===================== 日志配置 =====================

logging:
  level:
    # H3算法相关日志级别
    com.ict.ycwl.pathcalculate.algorithm.core.H3GeographicClustering: INFO
    com.ict.ycwl.pathcalculate.algorithm.core.UnifiedClusteringAdapter: INFO
    # H3库日志级别
    com.uber.h3core: WARN
  
  pattern:
    # 自定义日志格式，突出显示H3相关日志
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %highlight(%-5level) %cyan(%logger{36}) - %msg%n"

# ===================== 性能监控配置 =====================

management:
  endpoints:
    web:
      exposure:
        # 暴露健康检查和指标端点
        include: health,info,metrics
  endpoint:
    health:
      # 显示详细健康信息
      show-details: always
  metrics:
    tags:
      # 添加应用标识
      application: path-calculate-h3

# ===================== 应用信息配置 =====================

info:
  app:
    name: "粤北卷烟物流路径规划系统"
    description: "基于H3六边形网格的智能聚类算法"
    version: "3.0-H3"
    algorithm: "Uber H3 Geographic Clustering"
  build:
    artifact: "path-calculate"
    name: "path-calculate"
    time: "2025-08-09T16:19:08Z"
    version: "1.0-SNAPSHOT"

# ===================== 开发环境特殊配置 =====================

---
# 开发环境配置
spring:
  profiles: dev,development

# 开发环境启用详细日志和性能对比
clustering:
  algorithm:
    performance-comparison: true
    
logging:
  level:
    com.ict.ycwl.pathcalculate.algorithm: DEBUG
    root: INFO

---
# 生产环境配置
spring:
  profiles: prod,production

# 生产环境优化性能，关闭调试功能
clustering:
  algorithm:
    performance-comparison: false
    
logging:
  level:
    com.ict.ycwl.pathcalculate.algorithm.core: INFO
    root: WARN

# 生产环境资源限制
h3:
  quality:
    # 生产环境更严格的质量要求
    min-quality-ratio: 0.9