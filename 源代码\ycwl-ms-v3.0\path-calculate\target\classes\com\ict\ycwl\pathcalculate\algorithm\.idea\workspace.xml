<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="096bf07e-d95a-4c51-9a80-ab6e8e453439" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/core/ClusteringQualityEvaluator.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/debug/DebugDataExporter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/entity/ConflictResolution.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/entity/TimeBalanceAdjustment.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../resources/algorithm/data/v1.0/data_summary.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../resources/algorithm/debug/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../resources/visualization/debug.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../resources/visualization/route.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../resources/visualization/simple_route.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../../test/java/com/ict/ycwl/pathcalculate/algorithm/PathPlanningAlgorithmTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../../test/java/com/ict/ycwl/pathcalculate/algorithm/PathPlanningUtilsSimpleTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../../test/java/com/ict/ycwl/pathcalculate/algorithm/PathPlanningUtilsTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../../test/java/com/ict/ycwl/pathcalculate/algorithm/QuickTestRunner.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../../test/java/com/ict/ycwl/pathcalculate/algorithm/RunPathPlanningTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../../test/java/com/ict/ycwl/pathcalculate/algorithm/测试类使用指南.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../../test/resources/algorithm/data/v1.0/accumulations.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../../test/resources/algorithm/data/v1.0/data_summary.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../../test/resources/algorithm/data/v1.0/teams.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../../test/resources/algorithm/data/v1.0/time_matrix.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../../test/resources/algorithm/data/v1.0/transit_depots.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../../../../../../../test/resources/application-test.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../.idea/jarRepositories.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../.idea/jarRepositories.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/app.jar.original" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/app.jar.original" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/ClusterCalculateApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/ClusterCalculateApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/config/JacksonObjectMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/config/JacksonObjectMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/config/MybatisPlusConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/config/MybatisPlusConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/config/SwaggerConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/config/SwaggerConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/config/WebMvcConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/config/WebMvcConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/controller/ClusterController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/controller/ClusterController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/mapper/AccumulationMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/mapper/AccumulationMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/mapper/AreaMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/mapper/AreaMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/mapper/ErrorPointMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/mapper/ErrorPointMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/mapper/RouteMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/mapper/RouteMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/mapper/StoreMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/mapper/StoreMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/Accumulation.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/Accumulation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/Area$AreaBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/Area$AreaBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/Area.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/Area.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/Circle.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/Circle.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/DistanceEntry.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/DistanceEntry.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/DoublePoint.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/DoublePoint.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/ErrorPoint.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/ErrorPoint.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/ListErrorCluster.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/ListErrorCluster.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/ListErrorPointFather.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/ListErrorPointFather.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/ListErrorPointSon.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/ListErrorPointSon.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/ListPoint.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/ListPoint.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/LngAndLat.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/LngAndLat.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/MapResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/MapResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/MapResultPoint.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/MapResultPoint.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/ParameterListManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/ParameterListManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/Route.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/Route.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/Store.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/pojo/Store.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/CalculateService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/CalculateService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/Impl/CalculateServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/Impl/CalculateServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/Impl/ParameterServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/Impl/ParameterServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/Impl/PointServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/Impl/TestInformationImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/Impl/TestInformationImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/ParameterService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/ParameterService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/PointService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/PointService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/TestInformation.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/classes/com/ict/ycwl/clustercalculate/service/TestInformation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/createdFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../cluster-calculate/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/createdFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/constant/Constants.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/constant/Constants.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/constant/HttpStatus.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/constant/HttpStatus.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/text/CharsetKit.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/text/CharsetKit.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/text/Convert.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/text/Convert.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/text/StrFormatter.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/text/StrFormatter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/utils/FileUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/utils/FileUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/utils/MD5Util.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/utils/MD5Util.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/utils/StringUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/utils/StringUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/utils/Transfer.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/utils/Transfer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/web/AjaxResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/web/AjaxResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/web/Paging.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../common/target/classes/com/ict/ycwl/common/web/Paging.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../common/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../common/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/data-management.iml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/data-management.iml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/app.jar.original" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/app.jar.original" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/DataManagementApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/DataManagementApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/common/AutoFillMetaInfoHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/common/AutoFillMetaInfoHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/common/BaseResponse.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/common/BaseResponse.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/common/MyCustomException.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/common/MyCustomException.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/common/ResultUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/common/ResultUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/common/Static.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/common/Static.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/common/StatusCode.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/common/StatusCode.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/config/JsonConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/config/JsonConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/config/MyBatisPlusConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/config/MyBatisPlusConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/config/SwaggerConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/config/SwaggerConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/constant/OptionalDataType.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/constant/OptionalDataType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/CarController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/CarController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/CommonController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/CommonController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/DeliveryController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/DeliveryController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/RouteController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/RouteController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/StoreController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/StoreController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/SystemParameterController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/SystemParameterController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/TeamController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/TeamController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/TransitDepotController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/controller/TransitDepotController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/AccumulationDto.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/AccumulationDto.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/AddCarRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/AddCarRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/CarDownBox.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/CarDownBox.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/CarExcelFile.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/CarExcelFile.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/CarFile.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/CarFile.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/CarListRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/CarListRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/LicensePlateNumbersDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/LicensePlateNumbersDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/UpdateCarRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/UpdateCarRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/actual/AddCarActualRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/actual/AddCarActualRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/actual/CarActualListRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/actual/CarActualListRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/actual/UpdateCarActualRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/car/actual/UpdateCarActualRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/delivery/AddDeliveryRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/delivery/AddDeliveryRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/delivery/DeliveryListRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/delivery/DeliveryListRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/delivery/SelectTeamAndTransitDepotRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/delivery/SelectTeamAndTransitDepotRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/delivery/UpdateDeliveryRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/delivery/UpdateDeliveryRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/page/PageRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/page/PageRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/route/RouteExport.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/route/RouteExport.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/route/StoreVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/route/StoreVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/route/WorkTimeUpdateRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/route/WorkTimeUpdateRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/store/StoreAddRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/store/StoreAddRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/store/StoreCsvFile.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/store/StoreCsvFile.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/store/StoreExcelFile.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/store/StoreExcelFile.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/store/StoreListRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/store/StoreListRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/store/StoreUpdateRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/store/StoreUpdateRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/store/UpdateAreaRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/store/UpdateAreaRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/systemParameter/UpdateSystemParameterRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/systemParameter/UpdateSystemParameterRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/team/AddTeamRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/team/AddTeamRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/team/TeamListRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/team/TeamListRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/team/dto/TeamIdDto.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/team/dto/TeamIdDto.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/transitDepot/AddTransitDepot.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/transitDepot/AddTransitDepot.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/transitDepot/TransitDepotRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/transitDepot/TransitDepotRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/transitDepot/UpdateTransitDepotRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/dto/transitDepot/UpdateTransitDepotRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Accumulation.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Accumulation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Area.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Area.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Car.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Car.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/CarDriver.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/CarDriver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Delivery.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Delivery.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/DeliveryType.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/DeliveryType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/FileImportLogs.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/FileImportLogs.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Group.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Group.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/GroupArea.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/GroupArea.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Route.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Route.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/SecondTransit.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/SecondTransit.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Store$StoreBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Store$StoreBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Store.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Store.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/StoreTwo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/StoreTwo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/SystemParameter.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/SystemParameter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Team.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/Team.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/TransitDelivery.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/TransitDelivery.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/TransitDepot.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/entity/TransitDepot.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/info/DeliveryInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/info/DeliveryInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/info/TransitDepotInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/info/TransitDepotInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/AreaVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/AreaVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/GroupVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/GroupVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/RouteVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/RouteVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/StoreVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/StoreVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/carVO/AddCarDownBoxVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/carVO/AddCarDownBoxVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/carVO/CarActualDownBoxVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/carVO/CarActualDownBoxVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/carVO/CarActualVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/carVO/CarActualVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/carVO/CarVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/carVO/CarVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/deliveryVO/AddDeliveryDownBoxVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/deliveryVO/AddDeliveryDownBoxVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/deliveryVO/DeliveryVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/deliveryVO/DeliveryVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/deliveryVO/SearchFormDeliveryVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/deliveryVO/SearchFormDeliveryVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/storeVO/StoreDownBox.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/storeVO/StoreDownBox.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/storeVO/StoreExport.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/storeVO/StoreExport.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/storeVO/StoreListVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/storeVO/StoreListVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/ststemParameter/SecondTransitVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/ststemParameter/SecondTransitVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/ststemParameter/SystemParameterVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/ststemParameter/SystemParameterVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/teamVO/AddTeamInfoVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/teamVO/AddTeamInfoVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/teamVO/GetUpdateTeamInfoVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/teamVO/GetUpdateTeamInfoVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/teamVO/SearchFormVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/teamVO/SearchFormVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/teamVO/TeamVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/teamVO/TeamVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/transitDepotV0/TeamInfoVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/transitDepotV0/TeamInfoVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/transitDepotV0/TransitDepotVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/domain/vo/transitDepotV0/TransitDepotVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/exception/BusinessException.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/exception/BusinessException.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/exception/GlobalExceptionHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/exception/GlobalExceptionHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/AccumulationMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/AccumulationMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/AreaMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/AreaMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/CarDriverMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/CarDriverMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/CarMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/CarMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/DeliveryMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/DeliveryMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/DeliveryTypeMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/DeliveryTypeMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/FileImportLogsMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/FileImportLogsMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/GroupAreaMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/GroupAreaMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/GroupMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/GroupMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/RouteMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/RouteMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/SecondTransitMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/SecondTransitMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/StoreMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/StoreMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/StoreTwoMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/StoreTwoMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/SystemParameterMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/SystemParameterMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/TeamMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/TeamMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/TransitDeliveryMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/TransitDeliveryMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/TransitDepotMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/mapper/TransitDepotMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/AreaService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/AreaService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/CarService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/CarService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/DeliveryService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/DeliveryService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/FileImportLogsService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/FileImportLogsService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/GroupService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/GroupService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/PathFeignService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/PathFeignService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/RouteService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/RouteService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/StoreService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/StoreService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/SystemParameterService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/SystemParameterService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/TeamService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/TeamService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/TransitDepotService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/TransitDepotService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/AreaServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/AreaServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/CarServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/CarServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/DeliveryServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/DeliveryServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/FileImportLogsServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/FileImportLogsServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/GroupServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/GroupServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/RouteServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/RouteServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/StoreServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/StoreServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/SystemParameterServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/SystemParameterServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/TeamServiceImpl$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/TeamServiceImpl$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/TeamServiceImpl$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/TeamServiceImpl$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/TeamServiceImpl$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/TeamServiceImpl$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/TeamServiceImpl$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/TeamServiceImpl$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/TeamServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/TeamServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/TransitDepotServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/service/impl/TransitDepotServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/util/BeijingTimeUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/util/BeijingTimeUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/util/CsvImportUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/util/CsvImportUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/util/CsvUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/util/CsvUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/util/NumberChineseFormatterUtils$ChineseUnit.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/util/NumberChineseFormatterUtils$ChineseUnit.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/util/NumberChineseFormatterUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/classes/com/ict/datamanagement/util/NumberChineseFormatterUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../data-management/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/test-classes/com/ict/datamanagement/ExcelTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/test-classes/com/ict/datamanagement/Main.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/test-classes/com/ict/datamanagement/Test01.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/test-classes/com/ict/datamanagement/pojo/Address.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/test-classes/com/ict/datamanagement/pojo/Cunhui.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/test-classes/com/ict/datamanagement/pojo/Youjuxie.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../data-management/target/test-classes/com/ict/datamanagement/routeExportTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../gateway/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../gateway/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../gateway/target/app.jar.original" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../gateway/target/app.jar.original" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../gateway/target/classes/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../gateway/target/classes/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../gateway/target/classes/com/ict/ycwl/gateway/GatewayApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../gateway/target/classes/com/ict/ycwl/gateway/GatewayApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../gateway/target/classes/com/ict/ycwl/gateway/config/Swagger2ResourceProvider.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../gateway/target/classes/com/ict/ycwl/gateway/config/Swagger2ResourceProvider.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../gateway/target/classes/com/ict/ycwl/gateway/config/SwaggerConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../gateway/target/classes/com/ict/ycwl/gateway/config/SwaggerConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../gateway/target/classes/com/ict/ycwl/gateway/config/WebConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../gateway/target/classes/com/ict/ycwl/gateway/config/WebConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../gateway/target/classes/com/ict/ycwl/gateway/handler/SwaggerHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../gateway/target/classes/com/ict/ycwl/gateway/handler/SwaggerHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../gateway/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../gateway/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/app.jar.original" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/app.jar.original" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/GuestbookApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/GuestbookApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/TestApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/TestApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/feedback/FeedbackApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/feedback/FeedbackApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/form/FeedbackAddForm.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/form/FeedbackAddForm.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/form/FeedbackListForm$FeedbackListFormBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/form/FeedbackListForm$FeedbackListFormBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/form/FeedbackListForm.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/form/FeedbackListForm.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/form/ReplyAddForm.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/form/ReplyAddForm.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/group/GroupApi.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/group/GroupApi.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionAreaVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionAreaVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionRouteVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionRouteVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionSingleDataVo$ConditionSingleDataVoBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionSingleDataVo$ConditionSingleDataVoBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionSingleDataVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionSingleDataVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionStoreVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionStoreVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionUserVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionUserVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionsDataVo$ConditionsDataVoBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionsDataVo$ConditionsDataVoBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionsDataVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/ConditionsDataVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/FeedbackListVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/FeedbackListVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/FeedbackReplyVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/FeedbackReplyVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/GroupVo$GroupVoBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/GroupVo$GroupVoBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/GroupVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/GroupVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/UnhandledFeedbackVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/UnhandledFeedbackVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/UserVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/api/vo/UserVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/config/GlobalExceptionHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/config/GlobalExceptionHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/config/JacksonObjectMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/config/JacksonObjectMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/config/SwaggerConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/config/SwaggerConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/config/WebMvcConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/config/WebMvcConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/Area$AreaBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/Area$AreaBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/Area.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/Area.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/Feedback$FeedbackBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/Feedback$FeedbackBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/Feedback.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/Feedback.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/FeedbackFile$FeedbackFileBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/FeedbackFile$FeedbackFileBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/FeedbackFile.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/FeedbackFile.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/FeedbackReply$FeedbackReplyBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/FeedbackReply$FeedbackReplyBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/FeedbackReply.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/FeedbackReply.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/FeedbackReplyFile$FeedbackReplyFileBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/FeedbackReplyFile$FeedbackReplyFileBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/FeedbackReplyFile.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/FeedbackReplyFile.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/Group.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/Group.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/Route.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/Route.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/RouteAccumulation.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/RouteAccumulation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/Store.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/Store.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/TransitDepot.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/TransitDepot.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/User.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/domain/User.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/AreaMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/AreaMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/FeedbackFileMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/FeedbackFileMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/FeedbackMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/FeedbackMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/FeedbackReplyFileMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/FeedbackReplyFileMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/FeedbackReplyMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/FeedbackReplyMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/GroupMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/GroupMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/RouteAccumulationMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/RouteAccumulationMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/RouteMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/RouteMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/StoreMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/StoreMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/TransitDepotMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/TransitDepotMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/UserMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/mapper/UserMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/AreaService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/AreaService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/ConditionDataService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/ConditionDataService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/FeedbackReplyService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/FeedbackReplyService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/FeedbackService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/FeedbackService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/GroupService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/GroupService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/impl/AreaServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/impl/AreaServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/impl/ConditionDataServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/impl/ConditionDataServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/impl/FeedbackReplyServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/impl/FeedbackReplyServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/impl/FeedbackServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/impl/FeedbackServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/impl/GroupServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/classes/com/ict/ycwl/guestbook/service/impl/GroupServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../guestbook/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../pathcalculate.iml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../pathcalculate.iml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../controller/PathController.java" beforeDir="false" afterPath="$PROJECT_DIR$/../controller/PathController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../utils/MySQLConnection.java" beforeDir="false" afterPath="$PROJECT_DIR$/../utils/MySQLConnection.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../utils/pathOptimization/TSPData.java" beforeDir="false" afterPath="$PROJECT_DIR$/../utils/pathOptimization/TSPData.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../test/com/ict/ycwl/pathcalculate/RouteTest001.java" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../test/com/ict/ycwl/pathcalculate/RouteTest001.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../test/com/ict/ycwl/pathcalculate/utils/MySQLConnection.java" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../test/com/ict/ycwl/pathcalculate/utils/MySQLConnection.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/app.jar.original" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/classes/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../target/classes/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/classes/com/ict/ycwl/pathcalculate/controller/PathController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../target/classes/com/ict/ycwl/pathcalculate/controller/PathController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/classes/com/ict/ycwl/pathcalculate/utils/MySQLConnection.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../target/classes/com/ict/ycwl/pathcalculate/utils/MySQLConnection.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/classes/com/ict/ycwl/pathcalculate/utils/pathOptimization/TSPData.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../target/classes/com/ict/ycwl/pathcalculate/utils/pathOptimization/TSPData.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/maven-archiver/pom.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/PathCalculateApplication.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/Test01.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/TravelingSalesman$Node.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/TravelingSalesman.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/TravelingSalesmanHeldKarp$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/TravelingSalesmanHeldKarp$Index.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/TravelingSalesmanHeldKarp$SetSizeComparator.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/TravelingSalesmanHeldKarp.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/V6$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/V6$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/V6$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/V6$4.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/V6$5.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/V6$6.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/V6$7.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/V6$8.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/V6.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/WorkTimeTest$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/WorkTimeTest$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/WorkTimeTest$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/WorkTimeTest$4.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/WorkTimeTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/averageroutetest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/controller/AsyncController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/entity/PolarCoordinates.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/entity/PolarCoordinatesPath.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/pojoTest/AveTimeWorkTime.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/pojoTest/PointsSort.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/route/RouteTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/service/AsyncService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/service/Impl/RouteServiceImplTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/utils/AsyncTest.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/utils/GeneticAlgorithm.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/utils/GeoCoordinates.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/utils/MainRun.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/utils/MySQLConnection.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/utils/RouteTest01.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/utils/SpeciesIndividual.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/utils/SpeciesPopulation.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/utils/TSPData.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../target/test-classes/com/ict/ycwl/pathcalculate/v555.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../总(未经路线调整的班组平衡)route.txt" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../总(未经路线调整的班组平衡)route.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../pickup/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../pickup/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/src/main/java/com/ict/ycwl/user/pojo/User.java" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/src/main/java/com/ict/ycwl/user/pojo/User.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/src/main/java/com/ict/ycwl/user/service/impl/UserServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/src/main/java/com/ict/ycwl/user/service/impl/UserServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/app.jar.original" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/app.jar.original" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/UserServiceApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/UserServiceApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/annotation/PassToken.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/annotation/PassToken.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/annotation/UserLoginToken.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/annotation/UserLoginToken.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/config/CaptchaConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/config/CaptchaConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/config/InterceptorConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/config/InterceptorConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/config/MybatisPlusConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/config/MybatisPlusConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/config/SwaggerConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/config/SwaggerConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/config/TomcatConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/config/TomcatConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/config/WebMvcConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/config/WebMvcConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/controller/CaptchaController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/controller/CaptchaController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/controller/RoleOperationSetController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/controller/RoleOperationSetController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/controller/UserAvatarController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/controller/UserAvatarController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/controller/UserController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/controller/UserController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/controller/UserLoginController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/controller/UserLoginController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/controller/UserSearchController.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/controller/UserSearchController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/dao/GroupDao.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/dao/GroupDao.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/dao/OperationDao.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/dao/OperationDao.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/dao/RoleDao.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/dao/RoleDao.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/dao/RoleOperationDao.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/dao/RoleOperationDao.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/dao/UserDao.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/dao/UserDao.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/dao/UserGroupDao.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/dao/UserGroupDao.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/interceptor/AuthenticationInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/interceptor/AuthenticationInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/Group.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/Group.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/Operation.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/Operation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/Role.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/Role.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/RoleOperation.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/RoleOperation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/User.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/User.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/UserGroup.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/UserGroup.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/request/AddUserRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/request/AddUserRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/request/UpdateUserRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/pojo/request/UpdateUserRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/CaptchaService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/CaptchaService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/RoleService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/RoleService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/TokenService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/TokenService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/UserService.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/UserService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/impl/CaptchaServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/impl/CaptchaServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/impl/RoleServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/impl/RoleServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/impl/TokenServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/impl/TokenServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/impl/UserServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/classes/com/ict/ycwl/user/service/impl/UserServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../../user-service/target/test-classes/com/ict/ycwl/user/testdemo.class" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../../user-service/target/test-classes/com/ict/ycwl/user/testdemo.class" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../../../../../../../../.." />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="306IEcn4t8YA7Si232gNjsF4pXL" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;dart.analysis.tool.window.visible&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/src/main/java/com/ict/ycwl/pathcalculate/algorithm&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="096bf07e-d95a-4c51-9a80-ab6e8e453439" name="更改" comment="" />
      <created>1752941484973</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752941484973</updated>
      <workItem from="1752941486007" duration="5117000" />
      <workItem from="1753093208987" duration="2582000" />
      <workItem from="1753190434521" duration="7495000" />
      <workItem from="1753276142319" duration="2260000" />
      <workItem from="1753327421056" duration="1794000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/algorithm$extract_data.coverage" NAME="extract_data 覆盖结果" MODIFIED="1753278875603" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/data-extractor" />
  </component>
</project>